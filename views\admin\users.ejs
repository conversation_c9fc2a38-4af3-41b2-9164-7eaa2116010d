<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">User Management</h1>
      <p class="text-gray-400 mt-1">Manage user accounts, roles, and permissions</p>
    </div>
    <div class="flex items-center space-x-4">
      <button onclick="refreshUsers()" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-refresh mr-2"></i>
        Refresh
      </button>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Total Users</p>
        <p class="text-2xl font-bold text-white"><%= stats.total_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-users text-blue-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Active Users</p>
        <p class="text-2xl font-bold text-white"><%= stats.active_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-user-check text-green-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Admins</p>
        <p class="text-2xl font-bold text-white"><%= stats.admin_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-shield text-red-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Premium Users</p>
        <p class="text-2xl font-bold text-white"><%= stats.premium_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-crown text-yellow-400 text-xl"></i>
      </div>
    </div>
  </div>
</div>

<!-- Users Table -->
<div class="bg-dark-800 rounded-lg border border-gray-700">
  <div class="p-6 border-b border-gray-700">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-white">All Users</h3>
      <div class="flex items-center space-x-4">
        <div class="relative">
          <input type="text" placeholder="Search users..." 
                 class="bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
        </div>
      </div>
    </div>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full">
      <thead class="bg-gray-700">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Role</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Storage</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-700">
        <% users.forEach(function(user) { %>
          <tr class="hover:bg-dark-700/50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <i class="ti ti-user text-white"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-white"><%= user.username %></div>
                  <div class="text-sm text-gray-400"><%= user.email || 'No email' %></div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.role === 'admin' ? 'bg-red-100 text-red-800' : user.role === 'moderator' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' %>">
                <%= user.role %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= user.plan_type %></div>
              <div class="text-sm text-gray-400">
                <%= user.max_streaming_slots === -1 ? 'Unlimited' : user.max_streaming_slots %> slots, 
                <%= user.max_storage_gb %>GB
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= (user.used_storage_gb || 0).toFixed(2) %>GB used</div>
              <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                <% var percentage = user.max_storage_gb > 0 ? ((user.used_storage_gb || 0) / user.max_storage_gb * 100) : 0; %>
                <div class="bg-primary h-2 rounded-full" style="width: <%= Math.min(percentage, 100) %>%"></div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                <%= user.is_active ? 'Active' : 'Inactive' %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <button onclick="editUser('<%= user.id %>')" class="text-primary hover:text-primary-light">
                  <i class="ti ti-edit"></i>
                </button>
                <button onclick="toggleUserStatus('<%= user.id %>', <%= user.is_active ? 'false' : 'true' %>)" 
                        class="text-<%= user.is_active ? 'red' : 'green' %>-400 hover:text-<%= user.is_active ? 'red' : 'green' %>-300">
                  <i class="ti ti-<%= user.is_active ? 'user-off' : 'user-check' %>"></i>
                </button>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>
  </div>
</div>

<script>
  function refreshUsers() {
    window.location.reload();
  }

  function editUser(userId) {
    // TODO: Implement edit user modal
    alert('Edit user functionality coming soon!');
  }

  function toggleUserStatus(userId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'activate' : 'deactivate'} this user?`)) {
      fetch('/admin/users/toggle-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, isActive: newStatus === 'true' })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          window.location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to update user status');
      });
    }
  }
</script>
