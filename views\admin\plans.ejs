<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">Subscription Plans</h1>
      <p class="text-gray-400 mt-1">Manage subscription plans and pricing</p>
    </div>
    <div class="flex items-center space-x-4">
      <button onclick="addNewPlan()" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-plus mr-2"></i>
        Add Plan
      </button>
    </div>
  </div>
</div>

<!-- Plans Overview -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <% plans.forEach(function(plan) { %>
    <div class="bg-dark-800 rounded-lg border <%= plan.name === 'Pro' ? 'border-primary' : 'border-gray-700' %> relative overflow-hidden">
      <% if (plan.name === 'Pro') { %>
        <div class="absolute top-0 left-0 right-0 bg-primary text-white text-center py-2 text-sm font-medium">
          Most Popular
        </div>
      <% } %>
      
      <div class="p-6 <%= plan.name === 'Pro' ? 'pt-12' : '' %>">
        <!-- Plan Header -->
        <div class="text-center mb-6">
          <h3 class="text-xl font-bold text-white mb-2"><%= plan.name %></h3>
          <div class="mb-4">
            <span class="text-3xl font-bold text-white">$<%= plan.price %></span>
            <span class="text-gray-400">/<%= plan.billing_period %></span>
          </div>
        </div>

        <!-- Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-sm">
            <i class="ti ti-check text-green-400 mr-2"></i>
            <span class="text-gray-300">
              <%= plan.max_streaming_slots === -1 ? 'Unlimited' : plan.max_streaming_slots %> Streaming Slot<%= plan.max_streaming_slots !== 1 ? 's' : '' %>
            </span>
          </div>
          <div class="flex items-center text-sm">
            <i class="ti ti-check text-green-400 mr-2"></i>
            <span class="text-gray-300"><%= plan.max_storage_gb %>GB Storage</span>
          </div>
          <div class="flex items-center text-sm">
            <i class="ti ti-check text-green-400 mr-2"></i>
            <span class="text-gray-300">24/7 Support</span>
          </div>
        </div>

        <!-- Stats -->
        <div class="border-t border-gray-700 pt-4">
          <div class="text-center">
            <p class="text-sm text-gray-400">Active Subscribers</p>
            <p class="text-lg font-bold text-white"><%= plan.subscriber_count || 0 %></p>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-4 flex space-x-2">
          <button onclick="editPlan('<%= plan.id %>')" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors text-sm">
            <i class="ti ti-edit mr-1"></i>
            Edit
          </button>
          <% if (plan.name !== 'Free') { %>
            <button onclick="deletePlan('<%= plan.id %>')" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors text-sm">
              <i class="ti ti-trash"></i>
            </button>
          <% } %>
        </div>
      </div>
    </div>
  <% }); %>
</div>

<!-- Plans Table -->
<div class="bg-dark-800 rounded-lg border border-gray-700">
  <div class="p-6 border-b border-gray-700">
    <h3 class="text-lg font-semibold text-white">Plan Details</h3>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full">
      <thead class="bg-gray-700">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan Name</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Price</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Streaming Slots</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Storage</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Subscribers</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-700">
        <% plans.forEach(function(plan) { %>
          <tr class="hover:bg-dark-700/50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-<%= plan.name === 'Free' ? 'gray' : plan.name === 'Basic' ? 'blue' : plan.name === 'Pro' ? 'purple' : 'yellow' %>-500 rounded-lg flex items-center justify-center">
                  <i class="ti ti-<%= plan.name === 'Free' ? 'gift' : plan.name === 'Basic' ? 'star' : plan.name === 'Pro' ? 'crown' : 'diamond' %> text-white"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-white"><%= plan.name %></div>
                  <div class="text-sm text-gray-400"><%= plan.description || 'No description' %></div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white">$<%= plan.price %></div>
              <div class="text-sm text-gray-400">per <%= plan.billing_period %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white">
                <%= plan.max_streaming_slots === -1 ? 'Unlimited' : plan.max_streaming_slots %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= plan.max_storage_gb %>GB</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= plan.subscriber_count || 0 %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <button onclick="editPlan('<%= plan.id %>')" class="text-primary hover:text-primary-light">
                  <i class="ti ti-edit"></i>
                </button>
                <button onclick="viewSubscribers('<%= plan.id %>')" class="text-blue-400 hover:text-blue-300">
                  <i class="ti ti-users"></i>
                </button>
                <% if (plan.name !== 'Free') { %>
                  <button onclick="deletePlan('<%= plan.id %>')" class="text-red-400 hover:text-red-300">
                    <i class="ti ti-trash"></i>
                  </button>
                <% } %>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>
  </div>
</div>

<script>
  function addNewPlan() {
    // TODO: Implement add plan modal
    alert('Add new plan functionality coming soon!');
  }

  function editPlan(planId) {
    // TODO: Implement edit plan modal
    alert('Edit plan functionality coming soon!');
  }

  function viewSubscribers(planId) {
    // TODO: Implement view subscribers modal
    alert('View subscribers functionality coming soon!');
  }

  function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
      fetch('/admin/plans/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          window.location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to delete plan');
      });
    }
  }
</script>
